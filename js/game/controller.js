/**
 * 重构后的游戏控制器
 * 职责：作为协调器整合各个游戏子系统，提供统一的游戏控制接口
 * 
 * 架构特点：
 * - 采用组合模式，将复杂功能分解为专门化的子系统
 * - 保持向后兼容的API，确保现有代码无需修改
 * - 实现单一职责原则，每个子系统只负责自己的职责
 */
import Grid from './grid.js';
import Tetromino, { TETROMINO_SHAPES } from './tetromino.js';
import MatchChecker from './match-checker.js';
import GarbageGenerator from './garbage-generator.js';
import ComboSystem from './combo-system.js';
import ComboDisplay from '../ui/combo-display.js';
import ComboNotification from '../ui/combo-notification.js';
import { BLOCK_EFFECTS } from './block.js';
import TinyEmitter from '../libs/tinyemitter.js';
const Emitter = TinyEmitter.default || TinyEmitter;;

// 子系统
import { GameStateManager, GAME_STATE } from './state-management/game-state-manager.js';
import { GameFlowManager } from './state-management/game-flow-manager.js';
import { TetrominoManager } from './physics/tetromino-manager.js';
import { PhysicsEngine } from './physics/physics-engine.js';
import { CollisionDetector } from './physics/collision-detector.js';
import { ScoreManager } from './scoring/score-manager.js';
import { ComboManager } from './scoring/combo-manager.js';
import { MatchEngine } from './matching/match-engine.js';
import { EffectProcessor } from './matching/effect-processor.js';
import { GameRenderer } from './rendering/game-renderer.js';
import { AnimationManager } from './rendering/animation-manager.js';

// 游戏配置
const TETROMINO_COLOR_DISTRIBUTION = 'random';

export { GAME_STATE };

export default class RefactoredGameController extends Emitter {
  /**
   * 创建重构后的游戏控制器
   * @param {Object} options - 游戏配置
   */
  constructor(options = {}) {
    super();
    
    console.log('🎮 重构后的游戏控制器初始化开始 (Phase 3C-4)', options);
    
    // 保存配置选项
    this.options = {
      level: 1,
      colorCount: 4,
      effectProbability: 0.1,
      baseLineScore: 100,
      enableAchievements: true,
      enableCombos: true,
      ...options
    };
    
    // 初始化核心子系统
    this._initializeSubsystems();
    
    // 设置子系统之间的连接
    this._connectSubsystems();
    
    // 保持向后兼容的属性
    this._setupBackwardCompatibility();
    
    console.log('✅ 重构后的游戏控制器初始化完成 (Phase 3C-3)');
  }

  /**
   * 初始化各个子系统
   * @private
   */
  _initializeSubsystems() {
    console.log('🔧 初始化游戏子系统 (Phase 3C-3)...');
    
    // Phase 3C-1: 状态管理子系统
    this.stateManager = new GameStateManager();
    console.log('✅ 状态管理器已创建');
    
    this.flowManager = new GameFlowManager(this, this.stateManager);
    console.log('✅ 流程管理器已创建');
    
    // 首先初始化网格（其他系统依赖它）
    this._initializeGrid();
    
    // Phase 3C-2: 物理系统子系统
    this.tetrominoManager = new TetrominoManager(this.grid, {
      colorDistribution: 'random',
      colorCount: this.options.colorCount,
      effectProbability: this.options.effectProbability,
      allowedEffects: this.options.allowedEffects || ['frozen', 'mine']
    });
    console.log('✅ 方块管理器已创建');
    
    this.physicsEngine = new PhysicsEngine(this.grid, {
      fallSpeed: 30,
      softDropMultiplier: 20,
      lockDelay: 30
    });
    console.log('✅ 物理引擎已创建');
    
    this.collisionDetector = new CollisionDetector(this.grid, {
      enablePerformanceTracking: true,
      cacheCollisionResults: true
    });
    console.log('✅ 碰撞检测器已创建');
    
    // Phase 3C-3: 分数系统子系统
    this.scoreManager = new ScoreManager({
      baseLineScore: this.options.baseLineScore,
      levelMultiplier: 1.2,
      enableLevelProgression: true,
      enableAchievements: this.options.enableAchievements,
      linesPerLevel: 10,
      maxLevel: 20
    });
    console.log('✅ 分数管理器已创建');
    
    this.comboManager = new ComboManager({
      minComboLength: 2,
      comboTimeWindow: 3000,
      baseComboScore: 50,
      enableComboChains: true,
      enableComboEffects: this.options.enableCombos
    });
    console.log('✅ 连击管理器已创建');
    
    // Phase 3C-4: 匹配系统子系统
    this.matchEngine = new MatchEngine(this.grid, {
      enableLineClear: true,
      enableShapeMatch: false,
      enableColorMatch: false,
      enableSpecialBlocks: this.options.enableSpecialBlocks !== false,
      maxCascadeDepth: 10,
      cascadeDelay: 300
    });
    console.log('✅ 匹配引擎已创建');
    
    this.effectProcessor = new EffectProcessor({
      enableAnimations: this.options.enableAnimations !== false,
      enableParticles: this.options.enableParticles !== false,
      enableScreenShake: this.options.enableScreenShake !== false,
      animationSpeed: this.options.animationSpeed || 1.0,
      maxParticles: 100
    });
    console.log('✅ 特效处理器已创建');
    
    // Phase 3C-5: 渲染系统子系统
    this.gameRenderer = new GameRenderer(null, {
      enableLayeredRendering: this.options.enableLayeredRendering !== false,
      enableVsync: this.options.enableVsync !== false,
      enableDebugRendering: this.options.enableDebugRendering === true,
      targetFPS: this.options.targetFPS || 60,
      enableAntiAliasing: this.options.enableAntiAliasing !== false,
      enableOptimization: this.options.enableOptimization !== false
    });
    console.log('✅ 游戏渲染器已创建');
    
    this.animationManager = new AnimationManager({
      enableAnimations: this.options.enableAnimations !== false,
      defaultDuration: this.options.defaultAnimationDuration || 300,
      defaultEasing: this.options.defaultEasing || 'easeOutQuad',
      maxConcurrentAnimations: this.options.maxConcurrentAnimations || 50,
      globalTimeScale: this.options.animationTimeScale || 1.0,
      autoCleanup: this.options.autoCleanupAnimations !== false
    });
    console.log('✅ 动画管理器已创建');
    
    // 临时：原始组件（逐步迁移）
    this._initializeLegacyComponents();
  }

  /**
   * 初始化网格系统
   * @private
   */
  _initializeGrid() {
    // 同步创建网格（避免异步导致的undefined问题）
    this.grid = new Grid();
    console.log('🏗️ 网格系统已初始化');
  }

  /**
   * 临时初始化原始组件（向后兼容）
   * @private
   */
  _initializeLegacyComponents() {
    // 匹配检测器（将在Phase 3C-4迁移到MatchEngine）
    this.matchChecker = new MatchChecker(this.grid);
    console.log('✅ 匹配检测器已创建');

    // UI组件（将在Phase 3C-5迁移到GameRenderer）
    this.comboDisplay = new ComboDisplay({
      x: 20,
      y: 120,
      width: 200,
      height: 100
    });
    console.log('✅ 连击显示器已创建');

    this.comboNotification = new ComboNotification();
    console.log('✅ 连击通知器已创建');

    // 垃圾生成器（可能保留为独立系统）
    this.garbageGenerator = new GarbageGenerator({
      enabled: this.options.garbageEnabled !== false,
      level: this.options.level,
      colorCount: this.options.colorCount,
      allowedEffects: this.options.allowedEffects || ['frozen'],
      baseInterval: this.options.garbageInterval || 1800,
      baseDensity: this.options.garbageDensity || 0.6
    });
    this.garbageGenerator.setGrid(this.grid);
    console.log('✅ 垃圾生成器已创建');
  }

  /**
   * 连接各个子系统
   * @private
   */
  _connectSubsystems() {
    console.log('🔗 连接游戏子系统 (Phase 3C-3)...');
    
    // Phase 3C-1: 状态管理事件
    this.stateManager.on('stateChanged', (event) => {
      this.emit('stateChanged', event);
      console.log(`🎮 状态变化: ${event.from} → ${event.to}`);
    });
    
    this.flowManager.on('gameStart', (event) => {
      this.emit('gamestart', event);
    });
    
    this.flowManager.on('gamePause', (event) => {
      this.emit('gamepause', event);
    });
    
    this.flowManager.on('gameResume', (event) => {
      this.emit('gameresume', event);
    });
    
    this.flowManager.on('gameOver', (event) => {
      this.emit('gameover', event);
    });
    
    this.flowManager.on('gameReset', (event) => {
      this.emit('gamereset', event);
    });
    
    // Phase 3C-2: 物理系统事件
    this.tetrominoManager.on('tetromino:generated', (event) => {
      this.currentTetromino = event.tetromino;
      this.emit('tetromino:generated', event);
    });
    
    this.tetrominoManager.on('tetromino:locked', (event) => {
      this.currentTetromino = null;
      this.emit('tetromino:locked', event);
      
      // 锁定后处理物理效果
      this._handleTetrominoLocked(event);
    });
    
    this.tetrominoManager.on('tetromino:moved', (event) => {
      this.emit('tetromino:moved', event);
    });
    
    this.tetrominoManager.on('tetromino:rotated', (event) => {
      this.emit('tetromino:rotated', event);
    });
    
    this.physicsEngine.on('physics:blocksDropped', (event) => {
      this.emit('physics:blocksDropped', event);

      // 下落后可能需要检查匹配
      this._checkForMatches();
    });

    // 移除重力tick事件监听，改用原始的fallTimer机制
    
    this.physicsEngine.on('physics:floatingFixed', (event) => {
      this.emit('physics:floatingFixed', event);
    });
    
    this.collisionDetector.on('collision:checked', (event) => {
      if (this.options.debugMode) {
        console.log('🎯 碰撞检测:', event);
      }
    });
    
    // Phase 3C-3: 分数系统事件
    this.scoreManager.on('score:updated', (event) => {
      // 更新向后兼容的分数属性
      this.score = event.currentScore;
      this.emit('score:updated', event);
    });
    
    this.scoreManager.on('level:up', (event) => {
      // 更新游戏等级
      this.options.level = event.newLevel;
      this.emit('level:up', event);
      
      // 通知其他系统等级变化
      this._handleLevelUp(event);
    });
    
    this.scoreManager.on('achievement:unlocked', (event) => {
      this.emit('achievement:unlocked', event);
    });
    
    this.scoreManager.on('milestone:reached', (event) => {
      this.emit('milestone:reached', event);
    });
    
    this.comboManager.on('combo:started', (event) => {
      this.emit('combo:started', event);
    });
    
    this.comboManager.on('combo:continued', (event) => {
      // 更新向后兼容的连击属性
      this.combo = event.comboCount;
      this.emit('combo:continued', event);
      
      // 将连击分数添加到总分数
      this.scoreManager.addScore(event.comboScore, 'combo', {
        comboCount: event.comboCount,
        comboType: event.comboType
      });
    });
    
    this.comboManager.on('combo:ended', (event) => {
      this.combo = 0;
      this.emit('combo:ended', event);
    });
    
    this.comboManager.on('combo:broken', (event) => {
      this.combo = 0;
      this.emit('combo:broken', event);
    });
    
    this.comboManager.on('combo:milestone', (event) => {
      this.emit('combo:milestone', event);
    });
    
    this.comboManager.on('combo:effect', (event) => {
      this.emit('combo:effect', event);
    });
    
    // Phase 3C-4: 匹配系统事件
    this.matchEngine.on('matches:detected', (event) => {
      this.emit('matches:detected', event);
      console.log(`🔍 检测到${event.matches.length}个匹配`);
    });
    
    this.matchEngine.on('matches:processed', (event) => {
      this.emit('matches:processed', event);
      
      // 将匹配分数添加到总分数
      if (event.totalScore > 0 && this.scoreManager) {
        this.scoreManager.addScore(event.totalScore, 'match', {
          matchCount: event.processedMatches.length,
          cascadeTriggered: event.cascadeTriggered
        });
      }
      
      // 处理特效
      if (this.effectProcessor) {
        this._handleMatchEffects(event);
      }
    });
    
    this.matchEngine.on('blocks:removed', (event) => {
      this.emit('blocks:removed', event);
    });
    
    this.matchEngine.on('perfect:clear', (event) => {
      this.emit('perfect:clear', event);
      
      // 触发完美清除特效
      if (this.effectProcessor) {
        this.effectProcessor.triggerPerfectClearEffect();
      }
      
      // 完美清除奖励分数
      if (this.scoreManager) {
        const perfectBonus = this.scoreManager.calculateBonusScore('perfectClear');
        this.scoreManager.addScore(perfectBonus, 'perfect_clear');
      }
    });
    
    this.effectProcessor.on('effect:triggered', (event) => {
      this.emit('effect:triggered', event);
    });
    
    this.effectProcessor.on('screen:shake', (event) => {
      this.emit('screen:shake', event);
    });
    
    // Phase 3C-5: 渲染系统事件
    this.gameRenderer.on('renderer:initialized', (event) => {
      this.emit('renderer:initialized', event);
    });
    
    this.gameRenderer.on('renderer:resized', (event) => {
      this.emit('renderer:resized', event);
    });
    
    this.gameRenderer.on('frame:rendered', (event) => {
      this.emit('frame:rendered', event);
    });
    
    this.gameRenderer.on('renderer:reset', (event) => {
      this.emit('renderer:reset', event);
    });
    
    this.animationManager.on('animation:system:initialized', (event) => {
      this.emit('animation:system:initialized', event);
    });
    
    this.animationManager.on('animation:created', (event) => {
      this.emit('animation:created', event);
    });
    
    this.animationManager.on('animation:started', (event) => {
      this.emit('animation:started', event);
    });
    
    this.animationManager.on('animation:completed', (event) => {
      this.emit('animation:completed', event);
    });
    
    this.animationManager.on('sequence:started', (event) => {
      this.emit('sequence:started', event);
    });
    
    this.animationManager.on('sequence:completed', (event) => {
      this.emit('sequence:completed', event);
    });
    
    this.animationManager.on('group:completed', (event) => {
      this.emit('group:completed', event);
    });
    
    this.animationManager.on('animation:system:paused', (event) => {
      this.emit('animation:system:paused', event);
    });
    
    this.animationManager.on('animation:system:resumed', (event) => {
      this.emit('animation:system:resumed', event);
    });
    
    this.animationManager.on('animation:system:reset', (event) => {
      this.emit('animation:system:reset', event);
    });
    
    console.log('✅ 子系统连接完成 (Phase 3C-5)');
  }

  /**
   * 设置向后兼容性属性
   * @private
   */
  _setupBackwardCompatibility() {
    console.log('🔄 设置向后兼容性 (Phase 3C-3)...');
    
    // 向后兼容的游戏状态属性
    Object.defineProperty(this, 'state', {
      get: () => this.stateManager.getCurrentState(),
      set: (value) => this.stateManager.setState(value)
    });
    
    // 向后兼容的方块属性
    Object.defineProperty(this, 'currentTetromino', {
      get: () => this.tetrominoManager ? this.tetrominoManager.getCurrentTetromino() : null,
      set: (value) => {
        // 设置器主要用于兼容性，实际方块管理由TetrominoManager处理
        if (this.tetrominoManager && value === null) {
          // 允许清除当前方块
          this.tetrominoManager.currentTetromino = null;
        }
      }
    });
    
    Object.defineProperty(this, 'nextTetromino', {
      get: () => this.tetrominoManager ? this.tetrominoManager.getNextTetromino() : null
    });
    
    // Phase 3C-3: 向后兼容的分数和连击属性
    Object.defineProperty(this, 'score', {
      get: () => this.scoreManager ? this.scoreManager.getCurrentScore() : 0,
      set: (value) => {
        // 设置器用于兼容性，但实际分数管理由ScoreManager处理
        if (this.scoreManager && typeof value === 'number') {
          // 允许直接设置分数（用于调试或特殊情况）
          const currentScore = this.scoreManager.getCurrentScore();
          const difference = value - currentScore;
          if (difference !== 0) {
            this.scoreManager.addScore(difference, 'manual_set');
          }
        }
      }
    });
    
    Object.defineProperty(this, 'combo', {
      get: () => this.comboManager ? this.comboManager.getCurrentCombo() : 0,
      set: (value) => {
        // 设置器用于兼容性，但实际连击管理由ComboManager处理
        // 通常不应该直接设置连击值
      }
    });
    
    Object.defineProperty(this, 'level', {
      get: () => this.scoreManager ? this.scoreManager.getCurrentLevel() : this.options.level,
      set: (value) => {
        this.options.level = value;
        // 等级变化会影响多个系统
        this._handleLevelChange(value);
      }
    });
    
    // 其他向后兼容属性
    this.lastTetrominoPositions = null;

    // 计时器属性
    this.fallTimer = 0;
    this.lockTimer = 0;
    this.animationTimer = 0;
    this.effectsTimer = 0;

    // 软下降状态
    this.isSoftDropping = false;

    // 地雷爆炸相关状态
    this.lastRemovedPositions = [];
    
    // 锁定状态（现在由TetrominoManager管理）
    Object.defineProperty(this, 'lockResetCount', {
      get: () => this.tetrominoManager ? this.tetrominoManager.lockState.resetCount : 0
    });
    this.maxLockResets = 15;
    
    // 游戏控制状态
    Object.defineProperty(this, 'isPaused', {
      get: () => this.stateManager.isPaused()
    });
    
    Object.defineProperty(this, 'isSoftDropping', {
      get: () => this.physicsEngine ? this.physicsEngine.gravityState.isSoftDropping : false,
      set: (value) => {
        if (this.physicsEngine) {
          this.physicsEngine.setSoftDropping(value);
        }
      }
    });
    
    // 其他状态
    this.hasAnimations = false;
    this.hasMatches = false;
    this.effectsToApply = [];
    this.affectedBlocks = new Set();
    this.lastLockedBlocks = new Set();
    this.lastRemovedPositions = []; // 保存最近被移除的方块位置，用于重力计算
    this.pendingFullRowClear = null; // 待处理的满行清除信息
    this.lastLockedTetrominoInfo = null; // 保存最近锁定的方块组信息，用于解体检测
    this._pendingAfterAnimationCheck = false;
    this.destroyDuration = 20;

    // 🔧 新增：定时悬空检测
    this.floatingCheckInterval = 5000; // 5秒检测一次
    this.lastFloatingCheckTime = 0;

    // 🔧 新增：防止满行消除死循环
    this.isProcessingFullRows = false;
    this.lastFullRowClearTime = 0;
    
    console.log('✅ 向后兼容性设置完成 (Phase 3C-3)');
  }

  // =================== 游戏流程方法 ===================

  /**
   * 开始游戏
   */
  start() {
    console.log('🚀 RefactoredGameController.start() (Phase 3C-3)');

    // 启动垃圾生成器
    if (this.garbageGenerator) {
      this.garbageGenerator.start();
    }

    // 使用TetrominoManager生成第一个方块
    if (this.tetrominoManager) {
      this.tetrominoManager.generateNewTetromino();
    }

    // 启用物理引擎
    if (this.physicsEngine) {
      this.physicsEngine.setGravityEnabled(true);
    }

    // 设置状态为游戏中
    if (this.stateManager) {
      this.stateManager.setState(GAME_STATE.PLAYING);
    }

    console.log('✅ RefactoredGameController.start() 完成');
    return true;
  }

  /**
   * 暂停游戏
   */
  pause() {
    console.log('⏸️ RefactoredGameController.pause()');

    // 修改暂停逻辑，存储之前的状态
    if (this.stateManager.isState(GAME_STATE.PLAYING) ||
        this.stateManager.isState(GAME_STATE.ANIMATING) ||
        this.stateManager.isState(GAME_STATE.CHECKING)) {

      // 保存当前状态，以便恢复时知道之前是什么状态
      this.previousState = this.stateManager.getCurrentState();

      // 保存当前下落方块的状态
      if (this.currentTetromino) {
        this.savedTetrominoPosition = { ...this.currentTetromino.position };
        this.savedTetrominoRotation = this.currentTetromino.rotation;

        // 保存下落计时器状态
        this.savedFallTimer = this.fallTimer;
        this.savedLockTimer = this.lockTimer;
      }

      // 切换到暂停状态
      this.stateManager.setState(GAME_STATE.PAUSED);

      // 暂停物理引擎
      if (this.physicsEngine) {
        this.physicsEngine.setGravityEnabled(false);
      }

      // 暂停垃圾生成器
      if (this.garbageGenerator) {
        this.garbageGenerator.pause();
      }

      console.log('游戏暂停，之前状态：', this.previousState);
      this.emit('gamepause');

      return this.flowManager.pause({
        reason: 'manual',
        pauseTime: Date.now()
      });
    }
  }

  /**
   * 恢复游戏
   */
  resume() {
    console.log('▶️ RefactoredGameController.resume()');

    if (this.stateManager.isState(GAME_STATE.PAUSED)) {
      // 恢复之前的状态，如果没有保存则默认为PLAYING
      const targetState = this.previousState || GAME_STATE.PLAYING;
      this.stateManager.setState(targetState);
      this.previousState = null;

      // 恢复保存的方块状态
      if (this.currentTetromino && this.savedTetrominoPosition) {
        this.currentTetromino.position = { ...this.savedTetrominoPosition };
        this.currentTetromino.rotation = this.savedTetrominoRotation || 0;

        // 恢复计时器状态
        this.fallTimer = this.savedFallTimer || 0;
        this.lockTimer = this.savedLockTimer || 0;

        // 清除保存的状态
        this.savedTetrominoPosition = null;
        this.savedTetrominoRotation = null;
        this.savedFallTimer = null;
        this.savedLockTimer = null;

        // 设置恢复标志，防止立即生成新方块
        this.justResumed = true;
      }

      // 恢复物理引擎
      if (this.physicsEngine) {
        this.physicsEngine.setGravityEnabled(true);
      }

      // 恢复垃圾生成器
      if (this.garbageGenerator) {
        this.garbageGenerator.resume();
      }

      console.log('游戏恢复，当前状态：', targetState);
      this.emit('gameresume');

      const success = this.flowManager.resume({
        resumeTime: Date.now()
      });

      return success;
    }

    return false;
  }

  /**
   * 游戏结束
   */
  gameOver() {
    console.log('🏁 RefactoredGameController.gameOver()');
    
    // 停止物理引擎
    if (this.physicsEngine) {
      this.physicsEngine.setGravityEnabled(false);
    }
    
    // 结束当前连击
    if (this.comboManager) {
      this.comboManager.endCombo('game_over');
    }
    
    return this.flowManager.gameOver({
      reason: 'normal',
      score: this.score,
      level: this.level,
      combo: this.combo,
      endTime: Date.now()
    });
  }

  /**
   * 重置游戏
   */
  reset() {
    console.log('🔄 RefactoredGameController.reset()');
    
    // 重置物理系统
    if (this.tetrominoManager) {
      this.tetrominoManager.reset();
    }
    if (this.physicsEngine) {
      this.physicsEngine.reset();
    }
    if (this.collisionDetector) {
      this.collisionDetector.reset();
    }
    
    // 重置分数系统
    if (this.scoreManager) {
      this.scoreManager.reset();
    }
    if (this.comboManager) {
      this.comboManager.reset();
    }
    
    // 重置游戏数据
    this.lastTetrominoPositions = null;
    
    // 重置计时器
    this.fallTimer = 0;
    this.lockTimer = 0;
    this.animationTimer = 0;
    this.effectsTimer = 0;
    
    // 重置其他状态
    this.hasAnimations = false;
    this.hasMatches = false;
    this.effectsToApply = [];
    this.affectedBlocks.clear();
    this.lastLockedBlocks.clear();
    this.lastRemovedPositions = [];
    this._pendingAfterAnimationCheck = false;
    
    // 重置网格
    if (this.grid) {
      this.grid.clear();
    }
    
    // 停止垃圾生成器
    if (this.garbageGenerator) {
      this.garbageGenerator.stop();
    }
    
    // 重置网格
    if (this.grid) {
      this.grid.clear();
      console.log('🧹 网格已清空');
    }

    // 重置游戏状态
    if (this.stateManager) {
      this.stateManager.reset();
      console.log('🎮 游戏状态管理器已重置');
    }

    console.log('✅ RefactoredGameController.reset() 完成');
  }

  /**
   * 重新开始游戏
   */
  restart() {
    console.log('🔄 RefactoredGameController.restart()');
    return this.flowManager.restart();
  }

  /**
   * 更新关卡配置
   */
  updateLevel(level, config = {}) {
    this.options.level = level;
    
    // 更新垃圾生成器的关卡
    if (this.garbageGenerator) {
      this.garbageGenerator.updateLevel(level);
    }
    
    // 更新物理引擎参数
    if (this.physicsEngine && config.speedFactor) {
      this.physicsEngine.setGravity({
        fallSpeed: Math.floor(30 / config.speedFactor)
      });
    }
    
    // 更新方块管理器参数
    if (this.tetrominoManager) {
      if (config.colorCount) {
        this.tetrominoManager.options.colorCount = config.colorCount;
      }
      if (config.effectProbability) {
        this.tetrominoManager.options.effectProbability = config.effectProbability;
      }
      if (config.allowedEffects) {
        this.tetrominoManager.options.allowedEffects = config.allowedEffects;
      }
    }
    
    // Phase 3C-3: 通知分数管理器等级变化
    if (this.scoreManager && config.forceUpdate) {
      // 手动触发等级更新
      this.scoreManager.levelSystem.currentLevel = level;
    }
    
    // 更新其他配置
    if (config.colorCount) {
      this.options.colorCount = config.colorCount;
    }
    if (config.speedFactor) {
      this.options.speedFactor = config.speedFactor;
    }
    if (config.allowedEffects) {
      this.options.allowedEffects = config.allowedEffects;
    }
    
    console.log(`游戏控制器更新到关卡${level}`, this.options);
  }

  // =================== 向后兼容的输入处理方法 ===================

  /**
   * 处理左移操作（现在委托给TetrominoManager）
   * @private
   */
  _handleLeft() {
    if (!this.stateManager.isState(GAME_STATE.PLAYING)) return;
    
    if (this.tetrominoManager) {
      this.tetrominoManager.moveTetromino('left');
    } else {
      // 降级到原始实现
      this._legacyHandleLeft();
    }
  }

  /**
   * 处理右移操作（现在委托给TetrominoManager）
   * @private
   */
  _handleRight() {
    if (!this.stateManager.isState(GAME_STATE.PLAYING)) return;
    
    if (this.tetrominoManager) {
      this.tetrominoManager.moveTetromino('right');
    } else {
      // 降级到原始实现
      this._legacyHandleRight();
    }
  }

  /**
   * 处理旋转操作（现在委托给TetrominoManager）
   * @private
   */
  _handleRotate() {
    if (!this.stateManager.isState(GAME_STATE.PLAYING)) return;
    
    if (this.tetrominoManager) {
      this.tetrominoManager.rotateTetromino('cw');
    } else {
      // 降级到原始实现
      this._legacyHandleRotate();
    }
  }

  /**
   * 处理软下降
   * @param {boolean} isDown - 是否按下
   */
  _handleSoftDrop(isDown) {
    if (!this.stateManager.isState(GAME_STATE.PLAYING)) return;
    
    // 设置物理引擎的软下降状态
    if (this.physicsEngine) {
      this.physicsEngine.setSoftDropping(isDown);
    }
    
    // 如果按下且有当前方块，尝试立即下降一格
    if (isDown && this.tetrominoManager) {
      const moved = this.tetrominoManager.moveTetromino('down');
      if (moved && this.scoreManager) {
        // 软下降得分
        const dropScore = this.scoreManager.calculateDropScore(1, 'soft');
        this.scoreManager.addScore(dropScore, 'soft_drop');
      }
    }
  }

  /**
   * 处理硬降
   */
  _handleHardDrop() {
    if (!this.stateManager.isState(GAME_STATE.PLAYING)) return;
    
    if (this.tetrominoManager) {
      const dropDistance = this.tetrominoManager.hardDrop();
      
      // Phase 3C-3: 使用ScoreManager计算硬降分数
      if (dropDistance > 0 && this.scoreManager) {
        const dropScore = this.scoreManager.calculateDropScore(dropDistance, 'hard');
        this.scoreManager.addScore(dropScore, 'hard_drop', { distance: dropDistance });
      }
    }
  }

  // =================== 游戏逻辑处理方法 ===================

  /**
   * 处理自动下降逻辑（原始实现）
   * @private
   */
  _handleAutoFall() {
    // 游戏配置常量（从原始代码移植）
    const SOFT_DROP_SPEED = 20;  // 软下落的速度倍数
    const BASE_SPEED = 30;       // 基础下落间隔帧数
    const LOCK_DELAY = 30;       // 锁定延迟帧数

    // 计算下降速度（根据关卡调整）
    let fallSpeed;

    if (this.isSoftDropping) {
      // 快速下落：保持原有的高速度
      let baseSoftDropSpeed = Math.max(1, Math.floor(BASE_SPEED / SOFT_DROP_SPEED));
      fallSpeed = baseSoftDropSpeed;
    } else {
      // 自动下落：使用关卡调整的速度机制
      const baseFallSpeed = Math.max(15, BASE_SPEED - Math.floor(this.options.level / 10) * 2); // 每10级才减少2帧，最快15帧

      // 应用速度因子（speedFactor越大，下落越快）
      const speedFactor = this.options.speedFactor || 1.0;
      fallSpeed = Math.max(10, Math.floor(baseFallSpeed / speedFactor)); // 自动下落最快10帧
    }

    // 检查当前方块是否可以下落
    const canMoveDown = this.currentTetromino.canMoveDown(this.grid);

    // 如果不能下落，增加锁定计时器
    if (!canMoveDown) {
      this.lockTimer++;

      // 锁定延迟结束，锁定方块
      if (this.lockTimer >= LOCK_DELAY) {
        this._lockTetromino();
        return;
      }
    }

    // 方块自动下落逻辑（受fallTimer控制）
    this.fallTimer++;
    if (this.fallTimer >= fallSpeed) {
      this.fallTimer = 0;

      // 尝试下落（直接调用moveDown，无音效）
      if (canMoveDown) {
        this.currentTetromino.moveDown();
        this.lockTimer = 0; // 重置锁定计时器
      }
    }
  }

  /**
   * 锁定当前方块（原始实现）
   * @private
   */
  _lockTetromino() {
    if (!this.currentTetromino) return;

    console.log('🔒 锁定方块');

    // 保存当前方块的位置信息（用于后续处理）
    const positions = this.currentTetromino.getBlockPositions();
    this.lastTetrominoPositions = positions.map(({ row, col, block }) => ({
      row,
      col,
      block
    }));

    // 🧩 保存方块组信息用于解体检测
    this.lastLockedTetrominoInfo = {
      tetromino: this.currentTetromino,
      positions: [...positions], // 深拷贝位置信息
      shape: this.currentTetromino.shape,
      rotation: this.currentTetromino.rotation,
      centerPosition: { ...this.currentTetromino.position }
    };
    console.log(`🧩 保存锁定方块组信息: ${this.lastLockedTetrominoInfo.shape}, 位置数量: ${this.lastLockedTetrominoInfo.positions.length}`);

    // 放置方块到网格（原始实现）
    this.currentTetromino.placeOnGrid(this.grid);

    // 确保锁定状态
    this.currentTetromino.lock();

    // 检查游戏结束条件
    const isGameOver = positions.some(({ row }) => row <= 0);
    if (isGameOver) {
      console.log('方块锁定在顶行（row ≤ 0），游戏结束');
      this.gameOver();
      return;
    }

    // 清除当前方块
    this.currentTetromino = null;

    // 重置计时器
    this.fallTimer = 0;
    this.lockTimer = 0;
    this.isSoftDropping = false;

    // 切换到检查状态
    this.stateManager.setState(GAME_STATE.CHECKING);

    // 发出锁定事件
    this.emit('tetromino:lock', { positions });
  }

  /**
   * 检查匹配（原始实现）
   * @private
   */
  _checkMatches() {
    // 在所有消除检查前输出网格布局
    console.log('🗂️ 网格布局');
    this.grid.debugGridState('消除检查前的网格状态', true);

    // 先检查是否有满行需要消除
    if (this._checkAndClearFullRows()) {
      // 如果有满行被消除，就不需要再检查方块匹配了
      return;
    }

    // 使用匹配检测器检查匹配
    console.log('🔍 开始三消匹配检测');
    this.hasMatches = this.matchChecker.checkMatches();
    console.log(`🔍 三消匹配检测结果: ${this.hasMatches}, 匹配方块数: ${this.matchChecker.getMatchCount()}`);

    if (this.hasMatches) {
      console.log(`🔍 检测到方块匹配，开始消除流程，匹配方块数: ${this.matchChecker.getMatchCount()}`);

      // 开始消除动画
      this.effectsToApply = this.matchChecker.removeMatches();

      // 增加分数和连击
      const matchCount = this.matchChecker.getMatchCount();
      this.combo++;

      // 添加连击到连击系统
      if (this.comboSystem) {
        this.comboSystem.addCombo(matchCount, this.effectsToApply);
      }

      // 计算分数（使用连击系统的倍数）
      const comboMultiplier = this.comboSystem ? this.comboSystem.getComboMultiplier() : 1;
      const baseScore = matchCount * 10; // 基础分数
      const finalScore = Math.floor(baseScore * comboMultiplier);

      const oldScore = this.score;
      this.score += finalScore;

      console.log(`🎯 方块匹配消除! 匹配数: ${matchCount}, 连击: ${this.combo}, 分数: ${baseScore} × ${comboMultiplier.toFixed(1)}x = ${finalScore} (${oldScore} → ${this.score})`);

      // 发出匹配事件
      this.emit('blocks:match', { matchCount, score: finalScore, combo: this.combo });

      // 应用特殊效果
      if (this.effectsToApply && this.effectsToApply.length > 0) {
        this._applyEffects();
      }

      // 切换到动画状态
      this.stateManager.setState(GAME_STATE.ANIMATING);
      this.animationTimer = 0;
    } else {
      console.log('🔍 无匹配，生成新方块');

      // 无匹配，重置连击
      this.combo = 0;

      // 继续游戏，生成新的方块
      this._generateRandomTetromino();
      this.stateManager.setState(GAME_STATE.PLAYING);
    }
  }

  /**
   * 应用特殊效果（原始实现）
   * @private
   */
  _applyEffects() {
    if (!this.effectsToApply || this.effectsToApply.length === 0) return;

    console.log('🎆 应用特殊效果:', this.effectsToApply.length);

    // 去重效果
    const uniqueEffects = [];
    const effectKeys = new Set();

    for (const effect of this.effectsToApply) {
      const key = `${effect.type}-${effect.row}-${effect.col}`;
      if (!effectKeys.has(key)) {
        effectKeys.add(key);
        uniqueEffects.push(effect);
      }
    }

    console.log('去重后特效数量:', uniqueEffects.length);

    // 应用每个效果
    for (const effect of uniqueEffects) {
      console.log('处理特效:', effect.type, effect.row, effect.col);

      if (effect.type === BLOCK_EFFECTS.MINE) {
        // 处理地雷爆炸效果
        console.log('触发地雷效果:', effect.row, effect.col);
        const mineResult = this._handleMineEffect(effect);

        // 将地雷爆炸范围内的方块位置单独保存
        if (mineResult && mineResult.removedPositions) {
          this.lastRemovedPositions = [...this.lastRemovedPositions || [], ...mineResult.removedPositions];
        }
      }
      // 可以添加其他特殊效果处理
    }

    // 处理消除后的下落效果
    this._handleBlocksDrop(true);  // 传递true表示这是特殊效果触发的下落
  }

  /**
   * 处理方块消除后的下落效果
   * @param {boolean} isSpecialEffect - 是否为特殊效果导致的消除
   * @private
   */
  _handleBlocksDrop(isSpecialEffect = false) {
    console.log('📉 处理方块下落', { isSpecialEffect });

    // 收集所有被消除的方块位置
    const removedPositions = this._collectRemovedPositions();

    // 收集所有受影响的列
    const columnsToCheck = this._collectAffectedColumns(removedPositions);

    console.log(`📉 处理方块下落完成，影响列: ${Array.from(columnsToCheck).join(', ')}`);

    // 发出下落事件
    this.emit('blocks:drop', {
      isSpecialEffect,
      affectedColumns: Array.from(columnsToCheck),
      removedPositions: removedPositions.length
    });
  }

  /**
   * 收集所有被消除的方块位置
   * @private
   */
  _collectRemovedPositions() {
    const positions = [];
    if (this.matchChecker && this.matchChecker.matchedBlocks) {
      for (const block of this.matchChecker.matchedBlocks) {
        if (block && typeof block.row === 'number' && typeof block.col === 'number') {
          positions.push({ row: block.row, col: block.col });
        }
      }
    }
    return positions;
  }

  /**
   * 收集所有受影响的列
   * @private
   */
  _collectAffectedColumns(removedPositions) {
    const columnsToCheck = new Set();

    for (const position of removedPositions) {
      if (position && typeof position.col === 'number' && isFinite(position.col)) {
        columnsToCheck.add(position.col);
      }
    }

    return columnsToCheck;
  }

  /**
   * 处理地雷效果（原始实现）
   * @param {Object} effect - 效果对象
   * @private
   */
  _handleMineEffect(effect) {
    const { row, col } = effect;

    console.log('🗂️ 网格布局');
    this.grid.debugGridState('地雷爆炸前的网格状态', true);
    console.log('处理地雷效果，位置:', row, col);

    // 创建爆炸动画
    if (this.grid && typeof this.grid.createExplosionAnimation === 'function') {
      this.grid.createExplosionAnimation(row, col, 1);
    }

    // 获取3x3范围内的方块位置
    const removedPositions = [];
    const range = 1; // 爆炸范围

    // 检查周围的方块（3x3区域）
    for (let r = Math.max(0, row - range); r <= Math.min(this.grid.rows - 1, row + range); r++) {
      for (let c = Math.max(0, col - range); c <= Math.min(this.grid.cols - 1, col + range); c++) {
        // 跳过中心方块（地雷位置，已经被消除了）
        if (r === row && c === col) continue;

        // 获取当前位置的方块
        const targetBlock = this.grid.getBlock(r, c);
        console.log('检查周围的方块:', r, c, targetBlock);

        if (targetBlock && !targetBlock.isEmpty) {
          // 检查方块是否已经在消除中
          if (!targetBlock.isDestroying) {
            // 保存方块信息，包括方块对象本身
            removedPositions.push({
              row: r,
              col: c,
              block: targetBlock
            });

            // 标记方块为消除状态
            targetBlock.isDestroying = true;
            targetBlock.destroyAnimationFrame = 0;
            targetBlock.destroyProgress = 0;

            // 在地雷效果中直接移除方块，防止它在网格中残留
            this.grid.removeBlock(r, c);

            console.log('标记方块进行消除并从网格移除:', r, c);
          } else {
            console.log('方块已经在消除中:', r, c);
          }
        }
      }
    }

    // 播放爆炸音效
    if (GameGlobal.musicManager) {
      GameGlobal.musicManager.playEffect('explosion');
    }

    // 计算地雷爆炸得分
    const baseMineScore = this._calculateMineScore(removedPositions.length);
    let finalMineScore = 0;

    if (baseMineScore > 0) {
      const comboMultiplier = this.comboSystem ? this.comboSystem.getComboMultiplier() : 1;
      finalMineScore = Math.floor(baseMineScore * comboMultiplier);

      const oldScore = this.score;
      this.score += finalMineScore;

      console.log(`💣 地雷爆炸分数: ${baseMineScore} × ${comboMultiplier.toFixed(1)}x = ${finalMineScore} (${oldScore} → ${this.score})`);
    }

    // 发出效果事件
    this.emit('effect:mine', {
      row,
      col,
      affectedBlocks: removedPositions.length,
      score: finalMineScore
    });

    // 将被影响的方块列转换为Set以供其他方法使用
    const affectedColumns = new Set();
    for (const { col } of removedPositions) {
      affectedColumns.add(col);
    }

    // 确保地雷所在列也被检查
    affectedColumns.add(col);

    console.log('地雷消除位置总数:', removedPositions.length, removedPositions);

    return { affectedColumns, removedPositions };
  }

  /**
   * 计算地雷爆炸得分（原始实现）
   * @param {number} blocksCount - 爆炸消除的方块数量
   * @returns {number} 得分
   * @private
   */
  _calculateMineScore(blocksCount) {
    if (blocksCount === 0) return 0;

    // 地雷爆炸基础分数：每个方块30分
    const baseScore = blocksCount * 30;

    // 爆炸范围加成：消除越多方块，单个方块分数越高
    const rangeBonus = 1 + Math.min(blocksCount / 8, 1.5); // 最多2.5倍加成

    // 连击加成
    const comboBonus = 1 + (this.combo * 0.1);

    const finalScore = Math.floor(baseScore * rangeBonus * comboBonus);

    console.log(`地雷得分计算: 方块数: ${blocksCount}, 基础分: ${baseScore}, 最终分: ${finalScore}`);

    return finalScore;
  }

  /**
   * 处理动画状态（原始实现）
   * @private
   */
  _handleAnimationState() {
    this.animationTimer++;

    // 动画开始（第10帧）
    if (this.animationTimer === 10) {
      console.log('🎬 动画阶段: 开始消除动画');

      // 开始所有匹配方块的消除动画
      for (let row = 0; row < this.grid.rows; row++) {
        for (let col = 0; col < this.grid.cols; col++) {
          const block = this.grid.getBlock(row, col);
          if (block && this.matchChecker.matchedBlocks.has(block)) {
            block.startDestroyAnimation();
          }
        }
      }
    }
    // 检查消除动画是否完成
    else if (this.animationTimer >= 10 && this.animationTimer <= 30) {
      let allAnimationsComplete = true;

      // 更新所有正在消除的方块的动画
      for (let row = 0; row < this.grid.rows; row++) {
        for (let col = 0; col < this.grid.cols; col++) {
          const block = this.grid.getBlock(row, col);
          if (block && this.matchChecker.matchedBlocks.has(block)) {
            if (!block.updateDestroyAnimation()) {
              allAnimationsComplete = false;
            }
          }
        }
      }

      // 如果所有动画都完成了
      if (allAnimationsComplete || this.animationTimer === 30) {
        console.log('🎬 所有消除动画完成');

        // 🔧 检查是否是满行清除
        if (this.pendingFullRowClear) {
          console.log('� 处理满行清除完成');
          this._completeFullRowClear(this.pendingFullRowClear.fullRows);
          this.pendingFullRowClear = null;
        } else {
          // �🎯 重要修复：保存被移除的方块位置，用于重力计算
          this.lastRemovedPositions = [];

          // 移除所有匹配的方块
          let removedCount = 0;
          this.matchChecker.matchedBlocks.forEach(block => {
            if (block && typeof block.row === 'number' && typeof block.col === 'number') {
              const currentBlock = this.grid.getBlock(block.row, block.col);
              if (currentBlock === block) {
                // 保存位置信息
                this.lastRemovedPositions.push({
                  row: block.row,
                  col: block.col,
                  block: block
                });

                this.grid.removeBlock(block.row, block.col);
                removedCount++;
                console.log(`✅ 移除匹配方块 [${block.row}, ${block.col}]`);
              }
            }
          });

          console.log(`🧹 清理匹配状态: 移除了${removedCount}个方块`);
          this.matchChecker.matchedBlocks.clear();
          this.hasMatches = false;
          this.effectsToApply = [];

          // 重置动画计时器
          this.animationTimer = 0;

          // 检查是否有新的匹配
          this._checkForNewMatches();
        }
      }
    }
  }

  /**
   * 检查是否有新的匹配
   * @private
   */
  _checkForNewMatches(options = {}) {
    // If called from a process that has already handled gravity, skip it.
    if (!options.gravityHandled) {
      this._applyGravityAfterElimination();
    }

    // 🔧 修复：检查满行（重力后可能形成新的满行）
    const hasFullRows = this._checkAndClearFullRows();

    // 检查是否有新的三消匹配
    const hasNewMatches = this.matchChecker.checkMatches();

    if (hasNewMatches || hasFullRows) {
      if (hasNewMatches) {
        console.log('🔍 检测到新的三消匹配，触发连锁消除');
      }
      if (hasFullRows) {
        console.log('🔍 检测到新的满行，已开始清除');
        // 满行清除会自动处理，这里不需要额外操作
        return; // 满行清除会调用自己的完成回调
      }

      // 有新的匹配，继续消除流程
      this.effectsToApply = this.matchChecker.removeMatches();

      // 增加分数，连锁消除额外加分
      const matchCount = this.matchChecker.getMatchCount();
      this.combo++;

      // 添加连击到连击系统
      if (this.comboSystem) {
        this.comboSystem.addCombo(matchCount, this.effectsToApply);
      }

      // 计算分数
      const comboMultiplier = this.comboSystem ? this.comboSystem.getComboMultiplier() : 1;
      const baseScore = matchCount * 20; // 连锁消除基础分数更高
      const finalScore = Math.floor(baseScore * comboMultiplier);

      const oldScore = this.score;
      this.score += finalScore;

      console.log(`🔗 自动连锁消除! 连击: ${this.combo}, 分数: ${baseScore} × ${comboMultiplier.toFixed(1)}x = ${finalScore} (${oldScore} → ${this.score})`);

      // 重置动画计时器，进入新的消除动画
      this.animationTimer = 0;

      // 保持在动画状态
      this.stateManager.setState(GAME_STATE.ANIMATING);
    } else {
      console.log('🔍 没有新的匹配，回到游戏状态');

      // 无匹配，重置连击
      this.combo = 0;

      // 🧩 修复：在确认没有匹配时才清除锁定方块组信息
      this._clearLastLockedTetrominoInfo();

      // 生成新方块
      this._generateRandomTetromino();
      this.stateManager.setState(GAME_STATE.PLAYING);
    }
  }

  /**
   * 应用消除后的重力效果
   * @private
   */
  _applyGravityAfterElimination() {
    console.log('🌍 应用消除后的重力效果');

    if (!this.grid) {
      console.warn('网格对象不存在，跳过重力应用');
      return;
    }

    // 收集被消除的方块位置（从lastRemovedPositions获取）
    const removedPositions = this.lastRemovedPositions || [];

    // 收集受影响的列
    const affectedColumns = new Set();
    for (const pos of removedPositions) {
      if (pos && typeof pos.col === 'number') {
        affectedColumns.add(pos.col);
      }
    }

    // 🎯 新增：检测活动方块组是否需要解体
    const blocksToCheck = this._detectAndHandleTetrominoDisintegration(removedPositions);

    console.log(`🌍 重力参数: removedPositions=${removedPositions.length}, affectedColumns=[${Array.from(affectedColumns).join(', ')}], blocksToCheck=${blocksToCheck ? `Set(${blocksToCheck.size})` : 'undefined'}`);
    console.log(blocksToCheck)
    // 应用重力
    let hasFallen = false;

    if (removedPositions.length > 0) {
      // 有被消除的位置，使用精确重力
      hasFallen = this.grid.applyGravity(affectedColumns, blocksToCheck, removedPositions);
    } else {
      // 没有具体位置信息，使用全网格重力（调用不带参数的applyGravity）
      hasFallen = this.grid.applyGravity();
    }

    console.log(`🌍 重力应用结果: ${hasFallen ? '有方块下落' : '无方块下落'}`);

    // 清理临时数据
    this.lastRemovedPositions = [];

    return hasFallen;
  }

  /**
   * 检测并处理活动方块组的解体
   * @param {Array<{row: number, col: number, block: Block}>} removedPositions - 被消除的方块位置
   * @returns {Set<Block>|null} 需要独立重力检测的方块集合，如果没有则返回null
   * @private
   */
  _detectAndHandleTetrominoDisintegration(removedPositions) {
    // 🧩 修复：使用最近锁定的方块组信息而不是当前活动方块组
    if (!this.lastLockedTetrominoInfo) {
      console.log('🧩 没有最近锁定的方块组信息，跳过解体检测');
      return null;
    }

    const tetrominoInfo = this.lastLockedTetrominoInfo;
    const tetrominoBlocks = tetrominoInfo.positions;

    if (!tetrominoBlocks || tetrominoBlocks.length === 0) {
      console.log('🧩 锁定方块组没有子方块，跳过解体检测');
      return null;
    }

    console.log(`🧩 检测锁定方块组解体: ${tetrominoInfo.shape}, 子方块数量: ${tetrominoBlocks.length}`);

    // 检查锁定方块组是否有子方块参与了本次消除
    const participatingBlocks = this._findParticipatingBlocks(tetrominoBlocks, removedPositions);

    if (participatingBlocks.length === 0) {
      console.log('🧩 锁定方块组没有参与消除，跳过解体');
      return null;
    }

    console.log(`🧩 锁定方块组有 ${participatingBlocks.length} 个子方块参与消除，触发解体`);

    // 获取剩余的未消除子方块
    const remainingBlocks = this._getRemainingBlocks(tetrominoBlocks, participatingBlocks);

    if (remainingBlocks.length === 0) {
      console.log('🧩 锁定方块组完全消除，无剩余方块');
      this._clearLastLockedTetrominoInfo();
      return null;
    }

    console.log(`🧩 锁定方块组解体：${remainingBlocks.length} 个剩余方块将独立下落`);

    // 将剩余方块转换为独立方块并添加到重力检测
    const blocksToCheck = this._convertToIndependentBlocks(remainingBlocks);

    // 清除锁定方块组信息（因为已经解体）
    this._clearLastLockedTetrominoInfo();

    return blocksToCheck;
  }

  /**
   * 获取当前活动方块组
   * @returns {Tetromino|null} 当前活动方块组
   * @private
   */
  _getCurrentActiveTetromino() {
    // 优先从TetrominoManager获取
    if (this.tetrominoManager) {
      return this.tetrominoManager.getCurrentTetromino();
    }

    // 回退到直接属性
    return this.currentTetromino || null;
  }

  /**
   * 查找参与消除的方块
   * @param {Array<{row: number, col: number, block: Block}>} tetrominoBlocks - 方块组的所有子方块
   * @param {Array<{row: number, col: number, block: Block}>} removedPositions - 被消除的位置
   * @returns {Array<{row: number, col: number, block: Block}>} 参与消除的方块
   * @private
   */
  _findParticipatingBlocks(tetrominoBlocks, removedPositions) {
    const participatingBlocks = [];

    for (const tetrominoBlock of tetrominoBlocks) {
      for (const removedPos of removedPositions) {
        // 检查位置是否匹配
        if (tetrominoBlock.row === removedPos.row &&
            tetrominoBlock.col === removedPos.col) {
          participatingBlocks.push(tetrominoBlock);
          console.log(`🧩 发现参与消除的方块: [${tetrominoBlock.row}, ${tetrominoBlock.col}]`);
          break;
        }
      }
    }

    return participatingBlocks;
  }

  /**
   * 获取剩余的未消除方块
   * @param {Array<{row: number, col: number, block: Block}>} allBlocks - 所有方块
   * @param {Array<{row: number, col: number, block: Block}>} participatingBlocks - 参与消除的方块
   * @returns {Array<{row: number, col: number, block: Block}>} 剩余方块
   * @private
   */
  _getRemainingBlocks(allBlocks, participatingBlocks) {
    return allBlocks.filter(block => {
      return !participatingBlocks.some(participating =>
        participating.row === block.row && participating.col === block.col
      );
    });
  }

  /**
   * 将方块组的剩余方块转换为独立方块
   * @param {Array<{row: number, col: number, block: Block}>} remainingBlocks - 剩余方块
   * @returns {Set<Object>} 独立方块集合，每个元素包含 {block, row, col}
   * @private
   */
  _convertToIndependentBlocks(remainingBlocks) {
    const blocksToCheck = new Set();

    for (const blockInfo of remainingBlocks) {
      const { row, col, block } = blockInfo;

      // 确保方块在网格中的正确位置
      const gridBlock = this.grid.getBlock(row, col);
      if (gridBlock === block) {
        // 🧩 修复：创建包含位置信息的方块对象
        const blockWithPosition = {
          block: block,
          row: row,
          col: col
        };

        // 同时为方块对象添加位置属性（兼容性）
        block.row = row;
        block.col = col;

        blocksToCheck.add(blockWithPosition);
        console.log(`🧩 添加独立方块到重力检测: [${row}, ${col}]`);
      } else {
        console.warn(`🧩 方块位置不匹配: [${row}, ${col}]，跳过`);
      }
    }

    return blocksToCheck;
  }

  /**
   * 清除当前活动方块组
   * @private
   */
  _clearCurrentTetromino() {
    if (this.tetrominoManager) {
      // 如果使用TetrominoManager，通过它来清除
      this.tetrominoManager.currentTetromino = null;
    }

    // 同时清除直接属性
    this.currentTetromino = null;

    console.log('🧩 已清除当前活动方块组');
  }

  /**
   * 清除最近锁定的方块组信息
   * @private
   */
  _clearLastLockedTetrominoInfo() {
    this.lastLockedTetrominoInfo = null;
    console.log('🧩 已清除最近锁定的方块组信息');
  }

  /**
   * 检查并执行待生成的垃圾行
   * @private
   */
  _checkAndExecutePendingGarbage() {
    if (!this.garbageGenerator || !this.garbageGenerator.hasPendingGeneration()) {
      return;
    }

    console.log('🗑️ 检测到待生成的垃圾行，开始执行');

    const garbageEvent = this.garbageGenerator.executePendingGeneration();
    if (garbageEvent) {
      this._handleGarbageGenerationEvent(garbageEvent);
    }
  }

  /**
   * 处理垃圾行生成事件（简化版，不需要处理活动方块冲突）
   * @param {Object} event - 垃圾生成事件
   * @private
   */
  _handleGarbageGenerationEvent(event) {
    switch (event.type) {
      case 'generated':
        console.log(`✅ 生成了${event.rowCount}行垃圾方块，共${event.blockCount}个方块`);

        this.emit('garbagegenerated', {
          rowCount: event.rowCount,
          blockCount: event.blockCount,
          blocks: event.blocks
        });
        break;

      case 'failed':
        console.log('❌ 垃圾行生成失败:', event.reason);
        if (event.reason === 'no_space') {
          // 空间不足，游戏结束
          this.gameOver();
        }
        break;

      default:
        console.log('🗑️ 未知的垃圾生成事件:', event.type);
        break;
    }
  }

  /**
   * 生成新的随机方块（原始实现）
   * @private
   */
  _generateRandomTetromino() {
    console.log('🎲 生成新的活动方块');

    // 🗑️ 新增：在生成新方块前检查并执行待生成的垃圾行
    this._checkAndExecutePendingGarbage();

    // 🔧 新增：在生成新方块前输出当前网格状态
    if (this.grid && this.grid.debugGridState) {
      this.grid.debugGridState('生成新方块前的网格状态', true);
    }

    // 🧩 修复：不在这里清除方块组信息，延迟到确认没有消除时再清除
    // this._clearLastLockedTetrominoInfo(); // 移除立即清除

    // 委托给TetrominoManager生成新方块
    if (this.tetrominoManager) {
      const success = this.tetrominoManager.generateNewTetromino();
      if (!success) {
        // 无法生成新方块，游戏结束
        this.gameOver();
        return;
      }
    }
  }

  /**
   * 游戏结束（原始实现）
   * @private
   */
  gameOver() {
    console.log('🎮 游戏结束');

    // 设置游戏结束状态
    this.stateManager.setState(GAME_STATE.GAME_OVER);

    // 清除当前方块
    this.currentTetromino = null;

    // 重置计时器
    this.fallTimer = 0;
    this.lockTimer = 0;
    this.isSoftDropping = false;

    // 发出游戏结束事件
    this.emit('gameover', { score: this.score });
  }

  /**
   * 处理方块锁定后的逻辑
   * @param {Object} event - 锁定事件
   * @private
   */
  _handleTetrominoLocked(event) {
    console.log('🔒 处理方块锁定后逻辑 (Phase 3C-3)');

    // 🔧 新增：输出方块锁定后的网格状态
    if (this.grid && this.grid.debugGridState) {
      this.grid.debugGridState('方块锁定后的网格状态', true);
    }

    // 保存锁定的方块位置（用于匹配检测）
    this.lastLockedBlocks.clear();
    event.placedBlocks.forEach(block => {
      this.lastLockedBlocks.add(`${block.row},${block.col}`);
    });

    // 🔧 修复：先检查三消匹配，再生成新方块
    console.log('🔍 检查方块锁定后的三消匹配');
    this._checkForNewMatches();

    // 生成下一个方块
    if (this.tetrominoManager) {
      const success = this.tetrominoManager.generateNewTetromino();
      if (!success) {
        // 无法生成新方块，游戏结束
        this.gameOver();
        return;
      }
    }

    // 处理物理效果
    this._handlePhysicsAfterLock();
  }

  /**
   * 强制重置满行处理状态（调试用）
   * @private
   */
  _resetFullRowProcessingState() {
    console.log('🔧 强制重置满行处理状态');
    this.isProcessingFullRows = false;
    this.lastFullRowClearTime = 0;
  }

  /**
   * 检查并清除满行
   * @private
   */
  _checkAndClearFullRows() {
    console.log('�️ 网格布局');
    console.log('�🔍 检查满行');

    if (!this.grid) {
      console.warn('网格对象不存在，跳过满行检查');
      return false;
    }

    // 🔧 修复：防止死循环
    const currentTime = Date.now();
    if (this.isProcessingFullRows) {
      console.log('🔍 正在处理满行，跳过重复检查');

      // 🔧 新增：检查是否处理时间过长，强制重置
      if (currentTime - this.lastFullRowClearTime > 5000) { // 5秒超时
        console.log('⚠️ 满行处理超时，强制重置标记');
        this.isProcessingFullRows = false;
        this.lastFullRowClearTime = 0;
      } else {
        return false;
      }
    }

    // 防止过于频繁的满行检查
    if (currentTime - this.lastFullRowClearTime < 100) { // 100ms内不重复检查
      console.log('🔍 满行检查过于频繁，跳过');
      return false;
    }

    // 🔧 修复：实现真正的满行检测
    const fullRows = this._detectFullRows();

    if (fullRows.length === 0) {
      console.log('🔍 没有发现满行');
      return false;
    }

    console.log(`🔥 发现 ${fullRows.length} 个满行: [${fullRows.join(', ')}]`);

    // 🔧 修复：设置处理标记，防止死循环
    this.isProcessingFullRows = true;
    this.lastFullRowClearTime = currentTime;

    // 🔧 修复：先处理行清除分数，再开始动画
    this._handleLinesCleared(fullRows.length);

    // 开始满行清除动画
    this._clearFullRows(fullRows);

    return true;
  }

  /**
   * 检测满行
   * @returns {Array<number>} 满行的行号数组
   * @private
   */
  _detectFullRows() {
    const fullRows = [];

    for (let row = 0; row < this.grid.rows; row++) {
      let isFullRow = true;

      // 检查这一行是否完全被方块填满
      for (let col = 0; col < this.grid.cols; col++) {
        if (!this.grid.getBlock(row, col)) {
          isFullRow = false;
          break;
        }
      }

      if (isFullRow) {
        fullRows.push(row);
        console.log(`🔥 检测到满行: 第${row}行`);
      }
    }

    return fullRows;
  }

  /**
   * 清除满行
   * @param {Array<number>} fullRows - 要清除的行号数组
   * @private
   */
  _clearFullRows(fullRows) {
    console.log(`🔥 开始清除满行: [${fullRows.join(', ')}]`);

    // 🔧 修复：添加满行消除动画
    this._startFullRowClearAnimation(fullRows);
  }

  /**
   * 开始满行清除动画
   * @param {Array<number>} fullRows - 要清除的行号数组
   * @private
   */
  _startFullRowClearAnimation(fullRows) {
    console.log(`🎬 开始满行消除动画: [${fullRows.join(', ')}]`);

    // 从下往上排序，避免行号变化的问题
    fullRows.sort((a, b) => b - a);

    // 收集要消除的方块并开始动画
    const blocksToAnimate = [];
    const effectsToApply = []; // 🔥 新增：收集特殊效果

    for (const row of fullRows) {
      for (let col = 0; col < this.grid.cols; col++) {
        const block = this.grid.getBlock(row, col);
        if (block) {
          // 🔥 新增：检查方块的特殊效果
          if (block.effect && block.effect !== 'none') {
            console.log(`🎆 满行消除发现特效方块: ${block.effect}, 位置: (${row}, ${col})`);
            effectsToApply.push({
              type: block.effect,
              row: row,
              col: col
            });
          }

          // 🔥 修复：处理冰冻方块 - 满行消除时直接消除，不只是解冻
          if (block.effect === 'frozen' && block.isFrozen) {
            console.log(`🧊 满行消除：冰冻方块在行${row}列${col}直接消除（不只是解冻）`);
            block.setEffect('none'); // 先解除冰冻状态
          }

          // 开始消除动画
          block.startDestroyAnimation();
          blocksToAnimate.push({ block, row, col });

          // 🔧 修复：将满行方块添加到匹配列表，确保动画能够更新
          if (this.matchChecker) {
            this.matchChecker.matchedBlocks.add(block);
          }

          // console.log(`🎬 满行方块开始消除动画: [${row}, ${col}]`);
        }
      }
    }

    // 🔥 新增：应用收集到的特殊效果
    if (effectsToApply.length > 0) {
      console.log(`🎆 满行消除触发 ${effectsToApply.length} 个特殊效果:`, effectsToApply);
      this.effectsToApply = effectsToApply;

      // 应用特殊效果（如地雷爆炸）
      if (this.matchChecker) {
        const affectedBlocks = this.matchChecker.applyEffects(effectsToApply);
        console.log(`🎆 特殊效果影响了 ${affectedBlocks.size} 个额外方块`);
      }
    }

    // 🔧 修复：设置游戏状态为动画中，确保动画循环运行
    this.hasAnimations = true;

    // 🔧 关键修复：设置ANIMATING状态，确保_handleAnimationState被调用
    this.stateManager.setState(GAME_STATE.ANIMATING);

    // 🔧 重置动画计时器，让动画系统正确处理
    this.animationTimer = 0;

    // 设置动画完成后的回调
    this._scheduleFullRowClearCompletion(fullRows, blocksToAnimate);
  }

  /**
   * 安排满行清除完成处理
   * @param {Array<number>} fullRows - 要清除的行号数组
   * @param {Array} blocksToAnimate - 参与动画的方块数组
   * @private
   */
  _scheduleFullRowClearCompletion(fullRows, blocksToAnimate) {
    console.log(`🎬 安排满行清除完成检测，方块数量: ${blocksToAnimate.length}`);

    // 🔧 修复：让现有的动画系统处理满行消除
    // 不需要单独的检测逻辑，_handleAnimationState会处理所有动画
    console.log(`🎬 满行消除动画已交给现有动画系统处理`);

    // 保存满行信息，供动画完成后使用
    this.pendingFullRowClear = {
      fullRows: fullRows,
      blocksToAnimate: blocksToAnimate
    };
  }

  /**
   * 完成满行清除（动画结束后）
   * @param {Array<number>} fullRows - 要清除的行号数组
   * @private
   */
  _completeFullRowClear(fullRows) {
    console.log(`🎬 开始满行清除后的方块下降动画: [${fullRows.join(', ')}]`);

    // 先移除满行的方块
    for (const row of fullRows) {
      for (let col = 0; col < this.grid.cols; col++) {
        this.grid.removeBlock(row, col);
      }
    }

    // 使用动画系统处理上方方块的下降
    this._animateBlocksAfterRowClear(fullRows);
  }

  /**
   * 满行清除后的方块下降动画
   * @param {Array<number>} clearedRows - 被清除的行号数组
   * @private
   */
  _animateBlocksAfterRowClear(clearedRows) {
    console.log(`🎬 处理满行清除后的方块下降动画`);

    // 排序清除的行，从上到下
    const sortedClearedRows = [...clearedRows].sort((a, b) => a - b);

    // 🔥 修复：先收集所有需要移动的方块，避免覆盖问题
    const blocksToMove = [];

    // 从上往下遍历每一行，收集需要移动的方块
    for (let row = 0; row < this.grid.rows; row++) {
      // 跳过被清除的行
      if (sortedClearedRows.includes(row)) {
        continue;
      }

      // 计算这一行需要下降的距离
      const dropDistance = this._calculateDropDistance(row, sortedClearedRows);

      if (dropDistance > 0) {
        // 收集这一行的所有方块
        for (let col = 0; col < this.grid.cols; col++) {
          const block = this.grid.getBlock(row, col);
          if (block) {
            const targetRow = row + dropDistance;
            blocksToMove.push({
              block: block,
              fromRow: row,
              fromCol: col,
              toRow: targetRow,
              toCol: col
            });
          }
        }
      }
    }

    console.log(`🎬 收集到 ${blocksToMove.length} 个需要移动的方块`);

    // 🔥 修复：先移除所有需要移动的方块，再放置到新位置
    // 第一步：移除所有方块
    for (const moveInfo of blocksToMove) {
      this.grid.removeBlock(moveInfo.fromRow, moveInfo.fromCol);
      console.log(`🎬 移除方块: [${moveInfo.fromRow}, ${moveInfo.fromCol}]`);
    }

    // 第二步：放置到新位置并添加动画
    let hasAnimations = false;
    let animationDelay = 0;

    for (const moveInfo of blocksToMove) {
      // 放置到新位置
      this.grid.setBlock(moveInfo.toRow, moveInfo.toCol, moveInfo.block);

      // 更新方块的行属性
      moveInfo.block.row = moveInfo.toRow;

      console.log(`🎬 放置方块: [${moveInfo.fromRow}, ${moveInfo.fromCol}] → [${moveInfo.toRow}, ${moveInfo.toCol}]`);

      // 添加带延迟的下降动画
      if (this.grid.addRowClearFallingAnimation) {
        // 使用专门的满行清除动画方法
        this.grid.addRowClearFallingAnimation(
          moveInfo.block,
          moveInfo.fromRow,
          moveInfo.fromCol,
          moveInfo.toRow,
          moveInfo.toCol,
          animationDelay
        );
        hasAnimations = true;
        // console.log(`🎬 满行清除下降动画: [${moveInfo.fromRow}, ${moveInfo.fromCol}] → [${moveInfo.toRow}, ${moveInfo.toCol}], 延迟: ${animationDelay}ms`);
      } else if (this.grid.addFallingAnimation) {
        // 回退到普通下降动画
        setTimeout(() => {
          this.grid.addFallingAnimation(
            moveInfo.block,
            moveInfo.fromRow,
            moveInfo.fromCol,
            moveInfo.toRow,
            moveInfo.toCol
          );
        }, animationDelay);
        hasAnimations = true;
        // console.log(`🎬 方块下降动画: [${moveInfo.fromRow}, ${moveInfo.fromCol}] → [${moveInfo.toRow}, ${moveInfo.toCol}], 延迟: ${animationDelay}ms`);
      }

      // 每个方块增加一点延迟，创造波浪效果
      animationDelay += 30; // 30ms延迟
    }

    // 如果有动画，等待动画完成后检查新匹配
    if (hasAnimations) {
      console.log('🎬 等待满行清除下降动画完成...');

      // 计算最大下降距离来估算动画时间
      const maxDropDistance = Math.max(...sortedClearedRows.map(clearedRow =>
        this.grid.rows - 1 - clearedRow
      ));

      // 根据满行清除动画配置计算等待时间
      // 每行120ms + 最大延迟时间 + 缓冲时间
      const baseAnimationTime = Math.max(200, maxDropDistance * 120);
      const maxDelay = animationDelay; // 最后一个方块的延迟时间
      const bufferTime = 200; // 额外缓冲时间
      const estimatedAnimationTime = baseAnimationTime + maxDelay + bufferTime;

      console.log(`🎬 预计动画时间: ${estimatedAnimationTime}ms (最大下降距离: ${maxDropDistance}行, 最大延迟: ${maxDelay}ms)`);

      setTimeout(() => {
        this._onRowClearAnimationComplete();
      }, estimatedAnimationTime);
    } else {
      // 没有动画，直接完成
      this._onRowClearAnimationComplete();
    }
  }

  /**
   * 计算方块需要下降的距离
   * @param {number} row - 当前行
   * @param {Array<number>} clearedRows - 被清除的行号数组（已排序）
   * @returns {number} 下降距离
   * @private
   */
  _calculateDropDistance(row, clearedRows) {
    let dropDistance = 0;

    // 计算当前行下方有多少被清除的行
    for (const clearedRow of clearedRows) {
      if (clearedRow > row) {
        dropDistance++;
      }
    }

    return dropDistance;
  }

  /**
   * 满行清除动画完成后的处理
   * @private
   */
  _onRowClearAnimationComplete() {
    console.log('🎬 满行清除下降动画完成');

    // 检查新的匹配（可能产生连锁反应）
    this._checkForNewMatches({ gravityHandled: true });

    // 重置满行处理状态
    this.isProcessingFullRows = false;
    this.lastFullRowClearTime = 0;

    console.log('✅ 满行清除流程完成');
  }



  /**
   * 处理行清除
   * @param {number} linesCleared - 清除的行数
   * @param {Object} context - 清除上下文
   * @private
   */
  _handleLinesCleared(linesCleared, context = {}) {
    console.log(`🔥 行清除处理: ${linesCleared}行`);
    
    // Phase 3C-3: 使用ScoreManager计算分数
    if (this.scoreManager) {
      const lineScore = this.scoreManager.calculateLineScore(linesCleared, null, context);
      this.scoreManager.addScore(lineScore, 'lines', { 
        linesCleared, 
        context 
      });
      
      // 处理等级提升
      this.scoreManager.processLevelProgression(linesCleared);
    }
    
    // Phase 3C-3: 处理连击
    if (this.comboManager) {
      const actionData = {
        linesCleared,
        isTSpin: context.isTSpin || false,
        isPerfectClear: context.isPerfectClear || false,
        isDifficult: context.isDifficult || false
      };
      
      if (this.comboManager.comboState.isActive) {
        this.comboManager.continueCombo(actionData);
      } else {
        this.comboManager.startCombo(actionData);
      }
    }
    
    // 发出行清除事件
    this.emit('lines:cleared', {
      linesCleared,
      context,
      totalScore: this.score,
      currentCombo: this.combo
    });
  }

  /**
   * 处理等级提升
   * @param {Object} event - 等级提升事件
   * @private
   */
  _handleLevelUp(event) {
    console.log(`🆙 处理等级提升: ${event.oldLevel} → ${event.newLevel}`);
    
    // 更新游戏配置
    this.updateLevel(event.newLevel, {
      speedFactor: 1 + (event.newLevel - 1) * 0.1, // 每级增加10%速度
      forceUpdate: false // 避免重复更新
    });
    
    // 设置临时分数倍数奖励
    if (this.scoreManager) {
      this.scoreManager.setScoreMultiplier(
        1.2, 
        `level_up_${event.newLevel}`, 
        30000 // 30秒
      );
    }
  }

  /**
   * 处理等级变化
   * @param {number} newLevel - 新等级
   * @private
   */
  _handleLevelChange(newLevel) {
    // 通知各个系统等级变化
    console.log(`📊 等级变化处理: ${newLevel}`);
    
    // 可以在这里添加等级变化的其他处理逻辑
  }

  /**
   * 处理锁定后的物理效果
   * @private
   */
  _handlePhysicsAfterLock() {
    console.log('🌊 处理锁定后的物理效果');

    // 🔧 修复：添加全局悬空检测作为兜底机制
    this._performGlobalFloatingCheck();

    if (this.physicsEngine) {
      // 处理方块下落
      const hasDropped = this.physicsEngine.handleBlocksDrop();

      if (hasDropped) {
        // 如果有方块下落，检查浮动方块
        this.physicsEngine.smartFloatingCheck();
      }
    }
  }

  /**
   * 执行全局悬空检测（兜底机制）
   * @private
   */
  _performGlobalFloatingCheck() {
    console.log('🔍 执行全局悬空检测');

    if (!this.grid) {
      console.warn('网格对象不存在，跳过悬空检测');
      return false;
    }

    // 使用重力系统的悬空检测
    if (this.grid.detectAndHandleFloatingBlocks) {
      const hasFloatingBlocks = this.grid.detectAndHandleFloatingBlocks();
      console.log(`🔍 全局悬空检测结果: ${hasFloatingBlocks ? '发现悬空方块并处理' : '无悬空方块'}`);
      return hasFloatingBlocks;
    } else {
      // 如果没有新的悬空检测，使用简化版本
      return this._simpleFloatingCheck();
    }
  }

  /**
   * 简化的悬空检测实现
   * @returns {boolean} 是否有悬空方块被处理
   * @private
   */
  _simpleFloatingCheck() {
    console.log('🔍 执行简化悬空检测');

    let hasFloatingBlocks = false;
    const floatingBlocks = [];

    // 扫描所有方块，检查是否悬空
    for (let row = this.grid.rows - 2; row >= 0; row--) { // 从倒数第二行开始
      for (let col = 0; col < this.grid.cols; col++) {
        const block = this.grid.getBlock(row, col);
        if (block && this._isBlockFloating(row, col)) {
          floatingBlocks.push({ block, row, col });
          console.log(`🔍 发现悬空方块: [${row}, ${col}]`);
        }
      }
    }

    // 处理悬空方块
    if (floatingBlocks.length > 0) {
      console.log(`🔍 处理 ${floatingBlocks.length} 个悬空方块`);

      for (const { block, row, col } of floatingBlocks) {
        // 找到方块能下落到的最低位置
        const targetRow = this._findLowestAvailableRow(row, col);

        if (targetRow !== row) {
          console.log(`🔍 悬空方块下落: [${row}, ${col}] → [${targetRow}, ${col}]`);

          // 移动方块
          this.grid.removeBlock(row, col);
          this.grid.setBlock(targetRow, col, block);

          // 创建下落动画
          if (this.grid.addFallingAnimation) {
            this.grid.addFallingAnimation(block, row, col, targetRow, col);
          }

          hasFloatingBlocks = true;
        }
      }
    }

    return hasFloatingBlocks;
  }

  /**
   * 检查方块是否悬空（三点支撑检测）
   * @param {number} row - 方块行
   * @param {number} col - 方块列
   * @returns {boolean} 是否悬空
   * @private
   */
  _isBlockFloating(row, col) {
    // 检查左下、正下、右下三个位置
    const supportPositions = [
      { row: row + 1, col: col - 1 }, // 左下
      { row: row + 1, col: col },     // 正下
      { row: row + 1, col: col + 1 }  // 右下
    ];

    for (const pos of supportPositions) {
      // 如果位置超出边界，视为有支撑
      if (pos.row >= this.grid.rows || pos.col < 0 || pos.col >= this.grid.cols) {
        return false;
      }

      // 如果有任何一个支撑位置有方块，则不悬空
      if (this.grid.getBlock(pos.row, pos.col)) {
        return false;
      }
    }

    // 所有支撑位置都为空，方块悬空
    return true;
  }

  /**
   * 找到指定位置方块能够下落到的最低行
   * @param {number} startRow - 起始行
   * @param {number} col - 列索引
   * @returns {number} 目标行
   * @private
   */
  _findLowestAvailableRow(startRow, col) {
    let targetRow = startRow;

    // 从当前位置向下寻找第一个被占用的位置
    for (let row = startRow + 1; row < this.grid.rows; row++) {
      if (this.grid.getBlock(row, col) === null) {
        targetRow = row;
      } else {
        break; // 遇到方块就停止
      }
    }

    return targetRow;
  }

  /**
   * 更新定时悬空检测
   * @private
   */
  _updateFloatingCheck() {
    const currentTime = Date.now();

    // 检查是否到了执行悬空检测的时间
    if (currentTime - this.lastFloatingCheckTime >= this.floatingCheckInterval) {
      console.log('⏰ 定时悬空检测触发');

      // 只在游戏进行状态下执行悬空检测
      if (this.stateManager.isState(GAME_STATE.PLAYING)) {
        const hasFloatingBlocks = this._performGlobalFloatingCheck();

        if (hasFloatingBlocks) {
          console.log('⏰ 定时悬空检测发现并处理了悬空方块');

          // 如果发现悬空方块，检查是否形成新的匹配
          setTimeout(() => {
            this._checkForNewMatches();
          }, 500); // 等待下落动画完成
        }
      }

      this.lastFloatingCheckTime = currentTime;
    }
  }

  /**
   * 检查匹配
   * @private
   */
  _checkForMatches() {
    console.log('�️ 网格布局');
    this.grid.debugGridState('匹配检查前的网格状态', true);
    console.log('�🔍 检查匹配 (临时实现)');

    // 🔧 修复：实现真正的匹配检查，包括满行检测
    let hasAnyMatches = false;

    // 1. 检查三消匹配
    if (this.matchChecker) {
      const hasThreeMatches = this.matchChecker.checkMatches();
      if (hasThreeMatches) {
        console.log(`🔍 发现三消匹配，匹配方块数: ${this.matchChecker.getMatchCount()}`);
        hasAnyMatches = true;

        // 🔧 修复：处理三消匹配
        this._handleThreeMatchElimination();
      }
    }

    // 2. 检查满行
    const hasFullRows = this._checkAndClearFullRows();
    if (hasFullRows) {
      hasAnyMatches = true;
    }

    // 3. 如果没有任何匹配，回到游戏状态
    if (!hasAnyMatches) {
      console.log('🔍 没有新的匹配，回到游戏状态');
      // 这里可以设置游戏状态回到PLAYING
    }

    return hasAnyMatches;
  }

  /**
   * 处理三消匹配消除
   * @private
   */
  _handleThreeMatchElimination() {
    if (!this.matchChecker || this.matchChecker.getMatchCount() === 0) {
      console.log('🔍 没有三消匹配需要处理');
      return;
    }

    console.log('🗂️ 网格布局');
    this.grid.debugGridState('三消匹配消除前的网格状态', true);
    console.log(`🔥 开始处理三消匹配，消除 ${this.matchChecker.getMatchCount()} 个方块`);

    // 开始消除动画
    const matchedBlocks = Array.from(this.matchChecker.matchedBlocks);

    for (const block of matchedBlocks) {
      if (block && block.startDestroyAnimation) {
        block.startDestroyAnimation();
        console.log(`🎬 三消方块开始消除动画: [${block.row || '?'}, ${block.col || '?'}]`);
      }
    }

    // 设置动画状态
    this.hasAnimations = true;

    // 等待动画完成后实际移除方块
    setTimeout(() => {
      this._completeThreeMatchElimination(matchedBlocks);
    }, 333); // 等待消除动画完成
  }

  /**
   * 完成三消匹配消除
   * @param {Array} matchedBlocks - 匹配的方块数组
   * @private
   */
  _completeThreeMatchElimination(matchedBlocks) {
    console.log(`🔥 完成三消匹配消除，移除 ${matchedBlocks.length} 个方块`);

    const removedPositions = [];

    // 移除匹配的方块
    for (let row = 0; row < this.grid.rows; row++) {
      for (let col = 0; col < this.grid.cols; col++) {
        const block = this.grid.getBlock(row, col);
        if (block && matchedBlocks.includes(block)) {
          this.grid.removeBlock(row, col);
          removedPositions.push({ row, col });
          console.log(`🔥 移除三消方块: [${row}, ${col}]`);
        }
      }
    }

    // 清理匹配列表
    this.matchChecker.matchedBlocks.clear();

    // 应用重力
    if (removedPositions.length > 0) {
      console.log(`🌍 三消后应用重力，影响 ${removedPositions.length} 个位置`);

      // 收集受影响的列
      const affectedColumns = new Set();
      removedPositions.forEach(pos => affectedColumns.add(pos.col));

      // 应用重力
      const hasGravityEffect = this.grid.applyGravity(affectedColumns, null, removedPositions);
      console.log(`🌍 重力应用结果: ${hasGravityEffect ? '有方块下落' : '无方块下落'}`);

      // 等待重力完成后检查新的匹配
      setTimeout(() => {
        console.log('🔍 三消重力完成，检查新匹配');
        this._checkForNewMatches();
      }, 500);
    } else {
      console.log('🔍 三消完成，没有方块被移除');
    }
  }



  // =================== 降级实现（向后兼容） ===================

  /**
   * 降级左移实现
   * @private
   */
  _legacyHandleLeft() {
    if (!this.currentTetromino) return;
    
    this.currentTetromino.moveLeft();
    
    if (!this.currentTetromino.isValidPosition(this.grid)) {
      this.currentTetromino.moveRight();
      return;
    }
    
    this._resetLockTimerIfPossible();
    
    if (GameGlobal.musicManager && typeof GameGlobal.musicManager.playMove === 'function') {
      GameGlobal.musicManager.playMove();
    }
  }

  /**
   * 降级右移实现
   * @private
   */
  _legacyHandleRight() {
    if (!this.currentTetromino) return;
    
    this.currentTetromino.moveRight();
    
    if (!this.currentTetromino.isValidPosition(this.grid)) {
      this.currentTetromino.moveLeft();
      return;
    }
    
    this._resetLockTimerIfPossible();
    
    if (GameGlobal.musicManager && typeof GameGlobal.musicManager.playMove === 'function') {
      GameGlobal.musicManager.playMove();
    }
  }

  /**
   * 降级旋转实现
   * @private
   */
  _legacyHandleRotate() {
    if (!this.currentTetromino) return;
    
    const originalRotation = this.currentTetromino.rotation;
    this.currentTetromino.rotate();
    
    if (!this.currentTetromino.isValidPosition(this.grid)) {
      this.currentTetromino.rotation = originalRotation;
      // 注意：不需要调用updateBlocks()，因为getBlockPositions()是动态计算的
      return;
    }
    
    this._resetLockTimerIfPossible();
    
    if (GameGlobal.musicManager && typeof GameGlobal.musicManager.playRotate === 'function') {
      GameGlobal.musicManager.playRotate();
    }
  }

  /**
   * 重置锁定计时器（向后兼容）
   * @private
   */
  _resetLockTimerIfPossible() {
    if (this.tetrominoManager) {
      // 新实现由TetrominoManager处理
      return;
    }
    
    // 降级实现
    if (this.lockResetCount < this.maxLockResets) {
      this.lockTimer = 0;
      this.lockResetCount++;
    }
  }

  /**
   * 设置连击系统事件（临时方法）
   * @private
   */
  _setupComboSystemEvents() {
    // TODO: 在Phase 3C-3中已经移除，连击由ComboManager处理
    console.log('🎯 连击系统已由ComboManager接管');
  }

  // =================== 渲染和更新方法 ===================

  /**
   * 渲染游戏（向后兼容）
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  render(ctx) {
    // TODO: 在Phase 3C-5中迁移到GameRenderer
    
    // 渲染网格
    if (this.grid) {
      this.grid.render(ctx);
    }
    
    // 渲染当前下落中的方块
    if (this.currentTetromino && !this.currentTetromino.isLocked && this.grid) {
      // 🎯 恢复：渲染当前运动方块的底部投影虚影（重要的游戏功能）
      if (this.stateManager.isState(GAME_STATE.PLAYING)) {
        this.currentTetromino.renderGhost(ctx, this.grid);
      }

      // 渲染当前方块
      this.currentTetromino.render(ctx, this.grid);
    }

    // 渲染下一个方块的预览
    if (this.nextTetromino) {
      this._renderNextTetromino(ctx);
    }

    // 渲染分数
    this._renderScore(ctx);

    // 渲染游戏状态
    if (this.stateManager.isState(GAME_STATE.PAUSED)) {
      this._renderPauseScreen(ctx);
    } else if (this.stateManager.isState(GAME_STATE.GAME_OVER)) {
      this._renderGameOverScreen(ctx);
    }
    
    // 渲染UI组件
    if (this.comboDisplay) {
      // 更新连击显示数据
      if (this.comboManager) {
        const comboStats = this.comboManager.getComboStats();
        this.comboDisplay.updateStatus({
          combo: comboStats.currentCombo,
          energy: comboStats.energy,
          maxEnergy: comboStats.maxEnergy,
          comboStage: comboStats.comboStage,
          energyLevel: comboStats.energyLevel,
          patienceMultiplier: comboStats.patienceMultiplier || 1.0,
          patienceTime: comboStats.patienceTime || 0
        });
      }
      this.comboDisplay.render(ctx);
    }

    // 渲染连击通知（在最上层，确保不被遮挡）
    if (this.comboNotification) {
      this.comboNotification.render(ctx);
    }
  }

  /**
   * 渲染下一个方块预览
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @private
   */
  _renderNextTetromino(ctx) {
    const previewX = this.grid.offsetX + this.grid.cols * this.grid.blockSize + 20;
    const previewY = this.grid.offsetY;
    const previewSize = this.grid.blockSize * 0.8;

    // 绘制预览框
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    ctx.fillRect(
      previewX,
      previewY,
      previewSize * 4,
      previewSize * 4
    );

    // 绘制标题
    ctx.fillStyle = '#FFFFFF';
    ctx.font = '20px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('下一个', previewX + previewSize * 2, previewY - 10);

    // 获取方块位置
    const blockPositions = this.nextTetromino.getBlockPositions();

    // 计算预览居中偏移
    let minRow = Infinity;
    let maxRow = -Infinity;
    let minCol = Infinity;
    let maxCol = -Infinity;

    blockPositions.forEach(({ row, col }) => {
      minRow = Math.min(minRow, row);
      maxRow = Math.max(maxRow, row);
      minCol = Math.min(minCol, col);
      maxCol = Math.max(maxCol, col);
    });

    const width = maxCol - minCol + 1;
    const height = maxRow - minRow + 1;
    const offsetX = previewX + (4 - width) * previewSize / 2;
    const offsetY = previewY + (4 - height) * previewSize / 2;

    // 绘制方块
    blockPositions.forEach(({ row, col, block }) => {
      const x = offsetX + (col - minCol) * previewSize;
      const y = offsetY + (row - minRow) * previewSize;

      block.render(ctx, x, y, previewSize);
    });
  }

  /**
   * 渲染分数
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @private
   */
  _renderScore(ctx) {
    const scoreX = 20;
    const scoreY = 20;

    // 绘制分数背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(scoreX, scoreY, 200, 120);

    // 绘制分数文字
    ctx.fillStyle = '#FFFFFF';
    ctx.font = '24px Arial';
    ctx.textAlign = 'left';
    ctx.fillText(`分数: ${this.score}`, scoreX + 10, scoreY + 30);

    // 绘制关卡
    ctx.font = '20px Arial';
    ctx.fillText(`关卡: ${this.level}`, scoreX + 10, scoreY + 60);

    // 绘制连击
    if (this.combo > 0) {
      ctx.fillStyle = '#FFD700';
      ctx.fillText(`连击: ${this.combo}x`, scoreX + 10, scoreY + 90);
    }
  }

  /**
   * 渲染暂停画面
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @private
   */
  _renderPauseScreen(ctx) {
    // 半透明背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(0, 0, ctx.canvas.width, ctx.canvas.height);

    // 暂停文字
    ctx.fillStyle = '#FFFFFF';
    ctx.font = '36px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('游戏暂停', ctx.canvas.width / 2, ctx.canvas.height / 2 - 20);

    // 继续提示
    ctx.font = '20px Arial';
    ctx.fillText('点击屏幕继续', ctx.canvas.width / 2, ctx.canvas.height / 2 + 20);
  }

  /**
   * 渲染游戏结束画面
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @private
   */
  _renderGameOverScreen(ctx) {
    // 半透明背景
    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    ctx.fillRect(0, 0, ctx.canvas.width, ctx.canvas.height);

    // 游戏结束文字
    ctx.fillStyle = '#FF4136';
    ctx.font = '48px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('游戏结束', ctx.canvas.width / 2, ctx.canvas.height / 2 - 40);

    // 最终分数
    ctx.fillStyle = '#FFFFFF';
    ctx.font = '24px Arial';
    ctx.fillText(`最终分数: ${this.score}`, ctx.canvas.width / 2, ctx.canvas.height / 2 + 10);

    // 重新开始提示
    ctx.font = '20px Arial';
    ctx.fillText('点击屏幕重新开始', ctx.canvas.width / 2, ctx.canvas.height / 2 + 50);
  }

  /**
   * 更新游戏逻辑（向后兼容）
   */
  update() {
    if (this.stateManager.isPaused()) return;

    // 🔧 新增：定时悬空检测
    this._updateFloatingCheck();

    // 更新物理引擎
    if (this.physicsEngine) {
      this.physicsEngine.update();
    }

    // 更新方块管理器
    if (this.tetrominoManager) {
      // 检查锁定计时器
      if (this.tetrominoManager.updateLockTimer()) {
        this.tetrominoManager.lockTetromino();
      }
    }

    // Phase 3C-3: 更新连击管理器
    // ComboManager没有update方法，它是事件驱动的
    
    // 暂停状态处理（原始实现）
    if (this.stateManager.isState(GAME_STATE.PAUSED)) {
      // 检查是否有未完成的消除动画需要处理
      if (this.matchChecker && this.matchChecker.matchedBlocks.size > 0) {
        console.log(`🔥 暂停状态下发现未完成的消除动画，继续处理 ${this.matchChecker.matchedBlocks.size} 个方块`);

        // 强制移除所有标记为消除的方块
        let removedCount = 0;
        this.matchChecker.matchedBlocks.forEach(block => {
          if (block && typeof block.row === 'number' && typeof block.col === 'number') {
            // 强制移除方块
            const currentBlock = this.grid.getBlock(block.row, block.col);
            if (currentBlock === block) {
              this.grid.removeBlock(block.row, block.col);
              removedCount++;
              console.log(`✅ 暂停状态下强制移除方块 [${block.row}, ${block.col}]`);
            }
          }
        });

        if (removedCount > 0) {
          console.log(`🔥 暂停状态下完成移除 ${removedCount} 个方块`);

          // 清空匹配列表
          this.matchChecker.matchedBlocks.clear();
        }
      }

      // 在暂停状态下不执行其他逻辑更新，但保持方块可见
      return;
    }

    // 基本的计时器更新（向后兼容）
    if (this.stateManager.isState(GAME_STATE.PLAYING) && this.currentTetromino) {
      // 原始的自动下降逻辑（基于fallTimer）
      this._handleAutoFall();
    }

    // 检查匹配状态（原始实现）
    if (this.stateManager.isState(GAME_STATE.CHECKING)) {
      this._checkMatches();
    }

    // 动画状态，处理消除动画
    if (this.stateManager.isState(GAME_STATE.ANIMATING)) {
      this._handleAnimationState();
    }
    
    // 🎬 更新网格动画系统
    if (this.grid && this.grid.updateAnimations) {
      this.grid.updateAnimations();
    }

    // 更新UI组件
    if (this.comboDisplay) {
      this.comboDisplay.update();
    }
    if (this.comboNotification) {
      this.comboNotification.update();
    }

    // 更新垃圾生成器（仅更新计时器，不执行生成）
    if (this.garbageGenerator) {
      const garbageEvent = this.garbageGenerator.update();
      // 只处理预警事件，生成事件将在新方块生成前处理
      if (garbageEvent && garbageEvent.type === 'warning') {
        console.log(`垃圾生成预警：${garbageEvent.timeLeft}帧后生成`);
        this.emit('garbagewarning', { timeLeft: garbageEvent.timeLeft });
      } else if (garbageEvent && garbageEvent.type === 'pending') {
        console.log('🗑️ 垃圾行已标记为待生成');
      }
    }
  }

  // =================== 信息获取方法 ===================

  /**
   * 获取当前状态
   */
  getState() {
    return this.stateManager.getCurrentState();
  }

  /**
   * 获取状态信息
   */
  getStateInfo() {
    return {
      state: this.stateManager.getCurrentState(),
      isPaused: this.stateManager.isPaused(),
      isPlayable: this.stateManager.isPlayable(),
      score: this.score,
      combo: this.combo,
      level: this.level,
      flowState: this.flowManager.getFlowState(),
      debugInfo: this.stateManager.getDebugInfo(),
      // Phase 3C-2: 物理系统状态
      physicsState: this.physicsEngine ? this.physicsEngine.getState() : null,
      tetrominoState: this.tetrominoManager ? this.tetrominoManager.getState() : null,
      collisionStats: this.collisionDetector ? this.collisionDetector.getStats() : null,
      // Phase 3C-3: 分数系统状态
      scoreState: this.scoreManager ? this.scoreManager.getState() : null,
      comboState: this.comboManager ? this.comboManager.getComboStats() : null
    };
  }

  /**
   * 获取调试信息
   */
  getDebugInfo() {
    return {
      controller: 'RefactoredGameController',
      version: '3C-3',
      subsystems: {
        stateManager: !!this.stateManager,
        flowManager: !!this.flowManager,
        tetrominoManager: !!this.tetrominoManager,
        physicsEngine: !!this.physicsEngine,
        collisionDetector: !!this.collisionDetector,
        scoreManager: !!this.scoreManager,
        comboManager: !!this.comboManager,
        grid: !!this.grid,
        matchChecker: !!this.matchChecker,
        garbageGenerator: !!this.garbageGenerator
      },
      state: this.getStateInfo(),
      legacy: {
        score: this.score,
        combo: this.combo,
        level: this.level,
        fallTimer: this.fallTimer,
        lockTimer: this.lockTimer
      }
    };
  }

  /**
   * 销毁控制器并清理资源
   */
  destroy() {
    console.log('🧹 销毁重构后的游戏控制器 (Phase 3C-3)');
    
    // 销毁Phase 3C-3子系统
    if (this.scoreManager) {
      this.scoreManager.destroy();
    }
    if (this.comboManager) {
      this.comboManager.destroy();
    }
    
    // 销毁Phase 3C-2子系统
    if (this.tetrominoManager) {
      this.tetrominoManager.destroy();
    }
    if (this.physicsEngine) {
      this.physicsEngine.destroy();
    }
    if (this.collisionDetector) {
      this.collisionDetector.destroy();
    }
    
    // 销毁Phase 3C-1子系统
    if (this.stateManager) {
      this.stateManager.destroy();
    }
    if (this.flowManager) {
      this.flowManager.destroy();
    }
    
    // 清理原始组件
    if (this.garbageGenerator) {
      this.garbageGenerator.removeAllListeners();
    }
    
    // 清理自身的所有事件监听器
    this.removeAllListeners();
    
    console.log('✅ 重构后的游戏控制器清理完成 (Phase 3C-3)');
  }
} 